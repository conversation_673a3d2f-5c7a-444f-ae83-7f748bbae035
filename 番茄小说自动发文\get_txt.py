import requests
import time
import os
import re
from urllib.parse import urljoin, urlparse, parse_qs
import json

class FanqieNovelCrawler:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(self.headers)

    def extract_book_id(self, url):
        """从URL中提取书籍ID"""
        # 从URL中提取数字ID
        match = re.search(r'/(\d+)', url)
        if match:
            return match.group(1)
        return None

    def get_chapter_list(self, book_id):
        """获取章节列表"""
        # 构建章节列表API URL
        api_url = f"https://fanqienovel.com/api/author/library/chapter_list"

        params = {
            'book_id': book_id,
            'page_index': 1,
            'page_size': 1000,  # 获取大量章节
        }

        try:
            response = self.session.get(api_url, params=params)
            response.raise_for_status()

            data = response.json()
            if data.get('code') == 0:
                chapters = data.get('data', {}).get('chapters', [])
                print(f"找到 {len(chapters)} 个章节")
                return chapters
            else:
                print(f"获取章节列表失败: {data.get('message', '未知错误')}")
                return []

        except Exception as e:
            print(f"请求章节列表时出错: {e}")
            return []

    def get_chapter_content(self, chapter_id):
        """获取单个章节内容"""
        api_url = f"https://fanqienovel.com/api/author/library/chapter_content"

        params = {
            'chapter_id': chapter_id
        }

        try:
            response = self.session.get(api_url, params=params)
            response.raise_for_status()

            data = response.json()
            if data.get('code') == 0:
                content_data = data.get('data', {})
                return {
                    'title': content_data.get('title', ''),
                    'content': content_data.get('content', '')
                }
            else:
                print(f"获取章节内容失败: {data.get('message', '未知错误')}")
                return None

        except Exception as e:
            print(f"请求章节内容时出错: {e}")
            return None

    def clean_filename(self, filename):
        """清理文件名，移除不合法字符"""
        # 移除或替换不合法的文件名字符
        illegal_chars = r'[<>:"/\\|?*]'
        cleaned = re.sub(illegal_chars, '_', filename)
        # 限制文件名长度
        if len(cleaned) > 100:
            cleaned = cleaned[:100]
        return cleaned.strip()

    def save_chapter_to_file(self, chapter_data, output_dir):
        """保存章节到txt文件"""
        if not chapter_data:
            return False

        title = chapter_data['title']
        content = chapter_data['content']

        # 清理标题作为文件名
        filename = self.clean_filename(title) + '.txt'
        filepath = os.path.join(output_dir, filename)

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"{title}\n\n")
                f.write(content)

            print(f"已保存: {filename}")
            return True

        except Exception as e:
            print(f"保存文件失败 {filename}: {e}")
            return False

    def crawl_novel(self, management_url, output_dir="chapters"):
        """主要爬取函数"""
        print("开始爬取番茄小说章节...")

        # 提取书籍ID
        book_id = self.extract_book_id(management_url)
        if not book_id:
            print("无法从URL中提取书籍ID")
            return

        print(f"书籍ID: {book_id}")

        # 创建输出目录
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"创建输出目录: {output_dir}")

        # 获取章节列表
        chapters = self.get_chapter_list(book_id)
        if not chapters:
            print("未获取到章节列表")
            return

        # 逐个获取章节内容并保存
        success_count = 0
        total_count = len(chapters)

        for i, chapter in enumerate(chapters, 1):
            chapter_id = chapter.get('chapter_id')
            chapter_title = chapter.get('title', f'第{i}章')

            print(f"正在处理 ({i}/{total_count}): {chapter_title}")

            # 获取章节内容
            chapter_content = self.get_chapter_content(chapter_id)

            if chapter_content:
                # 保存到文件
                if self.save_chapter_to_file(chapter_content, output_dir):
                    success_count += 1

            # 添加延时避免请求过快
            time.sleep(1)

        print(f"\n爬取完成！成功保存 {success_count}/{total_count} 个章节")
        print(f"文件保存在: {os.path.abspath(output_dir)}")

def main():
    # 你的章节管理页面URL
    management_url = "https://fanqienovel.com/main/writer/chapter-manage/7533009356116216856&%E5%91%BD%E4%B9%A6%E6%AE%8B%E9%A1%B5?type=1"

    # 创建爬虫实例
    crawler = FanqieNovelCrawler()

    # 开始爬取
    crawler.crawl_novel(management_url, output_dir="novel_chapters")

if __name__ == "__main__":
    main()