import requests
import time
import os
import re
from bs4 import BeautifulSoup

class FanqieNovelCrawler:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(self.headers)

    def extract_book_id(self, url):
        """从URL中提取书籍ID"""
        # 从URL中提取数字ID
        match = re.search(r'/(\d+)', url)
        if match:
            return match.group(1)
        return None

    def get_chapter_list_from_page(self, management_url):
        """从章节管理页面获取章节列表"""
        try:
            print(f"正在访问章节管理页面: {management_url}")
            response = self.session.get(management_url)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            # 查找章节列表元素
            chapters = []

            # 尝试多种可能的章节列表选择器
            selectors = [
                '.chapter-item',
                '.chapter-list-item',
                '[data-chapter-id]',
                '.chapter-row',
                'tr[data-chapter-id]',
                '.chapter-title'
            ]

            for selector in selectors:
                chapter_elements = soup.select(selector)
                if chapter_elements:
                    print(f"找到章节元素，使用选择器: {selector}")
                    for element in chapter_elements:
                        chapter_info = self.extract_chapter_info(element)
                        if chapter_info:
                            chapters.append(chapter_info)
                    break

            if not chapters:
                # 如果没有找到章节，尝试查找所有链接
                print("未找到章节元素，尝试查找章节链接...")
                links = soup.find_all('a', href=True)
                for link in links:
                    href = link.get('href', '')
                    text = link.get_text(strip=True)
                    if 'chapter' in href.lower() or '章' in text:
                        chapters.append({
                            'title': text,
                            'url': href,
                            'chapter_id': self.extract_chapter_id_from_url(href)
                        })

            print(f"找到 {len(chapters)} 个章节")
            return chapters

        except Exception as e:
            print(f"获取章节列表时出错: {e}")
            return []

    def extract_chapter_info(self, element):
        """从章节元素中提取信息"""
        try:
            # 尝试获取章节标题
            title_element = element.find(['a', 'span', 'div'], class_=re.compile(r'title|name'))
            if not title_element:
                title_element = element.find('a') or element

            title = title_element.get_text(strip=True) if title_element else "未知章节"

            # 尝试获取章节链接
            link_element = element.find('a', href=True)
            if link_element:
                url = link_element.get('href')
                chapter_id = self.extract_chapter_id_from_url(url)
            else:
                url = None
                chapter_id = element.get('data-chapter-id') or element.get('data-id')

            return {
                'title': title,
                'url': url,
                'chapter_id': chapter_id
            }

        except Exception as e:
            print(f"提取章节信息时出错: {e}")
            return None

    def extract_chapter_id_from_url(self, url):
        """从URL中提取章节ID"""
        if not url:
            return None

        # 尝试从URL中提取数字ID
        match = re.search(r'/(\d+)', url)
        if match:
            return match.group(1)

        # 尝试从查询参数中提取
        if '?' in url:
            params = url.split('?')[1]
            for param in params.split('&'):
                if 'chapter' in param.lower() and '=' in param:
                    value = param.split('=')[1]
                    if value.isdigit():
                        return value

        return None

    def get_chapter_content(self, chapter_id):
        """获取单个章节内容"""
        api_url = f"https://fanqienovel.com/api/author/library/chapter_content"

        params = {
            'chapter_id': chapter_id
        }

        try:
            response = self.session.get(api_url, params=params)
            response.raise_for_status()

            data = response.json()
            if data.get('code') == 0:
                content_data = data.get('data', {})
                return {
                    'title': content_data.get('title', ''),
                    'content': content_data.get('content', '')
                }
            else:
                print(f"获取章节内容失败: {data.get('message', '未知错误')}")
                return None

        except Exception as e:
            print(f"请求章节内容时出错: {e}")
            return None

    def clean_filename(self, filename):
        """清理文件名，移除不合法字符"""
        # 移除或替换不合法的文件名字符
        illegal_chars = r'[<>:"/\\|?*]'
        cleaned = re.sub(illegal_chars, '_', filename)
        # 限制文件名长度
        if len(cleaned) > 100:
            cleaned = cleaned[:100]
        return cleaned.strip()

    def save_chapter_to_file(self, chapter_data, output_dir):
        """保存章节到txt文件"""
        if not chapter_data:
            return False

        title = chapter_data['title']
        content = chapter_data['content']

        # 清理标题作为文件名
        filename = self.clean_filename(title) + '.txt'
        filepath = os.path.join(output_dir, filename)

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"{title}\n\n")
                f.write(content)

            print(f"已保存: {filename}")
            return True

        except Exception as e:
            print(f"保存文件失败 {filename}: {e}")
            return False

    def get_chapter_content_from_page(self, chapter_url):
        """从章节页面获取内容"""
        try:
            if not chapter_url.startswith('http'):
                chapter_url = 'https://fanqienovel.com' + chapter_url

            print(f"正在获取章节内容: {chapter_url}")
            response = self.session.get(chapter_url)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            # 尝试获取章节标题
            title_selectors = [
                'h1',
                '.chapter-title',
                '.title',
                '[class*="title"]'
            ]

            title = "未知章节"
            for selector in title_selectors:
                title_element = soup.select_one(selector)
                if title_element:
                    title = title_element.get_text(strip=True)
                    break

            # 尝试获取章节内容
            content_selectors = [
                '.chapter-content',
                '.content',
                '.article-content',
                '[class*="content"]',
                '.text-content'
            ]

            content = ""
            for selector in content_selectors:
                content_element = soup.select_one(selector)
                if content_element:
                    # 获取文本内容，保留段落结构
                    paragraphs = content_element.find_all(['p', 'div'])
                    if paragraphs:
                        content = '\n\n'.join([p.get_text(strip=True) for p in paragraphs if p.get_text(strip=True)])
                    else:
                        content = content_element.get_text(strip=True)
                    break

            if not content:
                print(f"未找到章节内容: {title}")
                return None

            return {
                'title': title,
                'content': content
            }

        except Exception as e:
            print(f"获取章节内容时出错: {e}")
            return None

    def crawl_novel(self, management_url, output_dir="chapters"):
        """主要爬取函数"""
        print("开始爬取番茄小说章节...")

        # 创建输出目录
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"创建输出目录: {output_dir}")

        # 从章节管理页面获取章节列表
        chapters = self.get_chapter_list_from_page(management_url)
        if not chapters:
            print("未获取到章节列表")
            return

        # 逐个获取章节内容并保存
        success_count = 0
        total_count = len(chapters)

        for i, chapter in enumerate(chapters, 1):
            chapter_url = chapter.get('url')
            chapter_title = chapter.get('title', f'第{i}章')

            print(f"正在处理 ({i}/{total_count}): {chapter_title}")

            if not chapter_url:
                print(f"跳过章节（无URL）: {chapter_title}")
                continue

            # 获取章节内容
            chapter_content = self.get_chapter_content_from_page(chapter_url)

            if chapter_content:
                # 保存到文件
                if self.save_chapter_to_file(chapter_content, output_dir):
                    success_count += 1

            # 添加延时避免请求过快
            time.sleep(2)

        print(f"\n爬取完成！成功保存 {success_count}/{total_count} 个章节")
        print(f"文件保存在: {os.path.abspath(output_dir)}")

def main():
    # 你的章节管理页面URL
    management_url = "https://fanqienovel.com/main/writer/chapter-manage/7533009356116216856&%E5%91%BD%E4%B9%A6%E6%AE%8B%E9%A1%B5?type=1"

    # 创建爬虫实例
    crawler = FanqieNovelCrawler()

    # 开始爬取
    crawler.crawl_novel(management_url, output_dir="novel_chapters")

if __name__ == "__main__":
    main()